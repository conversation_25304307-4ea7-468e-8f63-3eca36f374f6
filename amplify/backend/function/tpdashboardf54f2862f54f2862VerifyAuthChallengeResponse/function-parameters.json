{"trigger": true, "modules": ["boilerplate-verify"], "parentResource": "tpdashboardf54f2862f54f2862", "functionName": "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse", "resourceName": "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/repo/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/VerifyAuthChallengeResponse", "triggerTemplate": "VerifyAuthChallengeResponse.json.ejs", "triggerEventPath": "VerifyAuthChallengeResponse.event.json", "roleName": "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse", "skipEdit": true}