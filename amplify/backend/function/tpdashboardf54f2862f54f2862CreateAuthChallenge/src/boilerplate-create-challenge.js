/**
 * @type {import('@types/aws-lambda').CreateAuthChallengeTriggerHandler}
 */

const AWS = require('aws-sdk');
const Handlebars = require('handlebars');
var ses = new AWS.SES();

exports.handler = async (event) => {
  const email = event.request.userAttributes.email;
  console.log(event);

  if (event.request.session.length === 2 && event.request.challengeName === 'CUSTOM_CHALLENGE') {
    event.response.publicChallengeParameters = { trigger: 'true' };

    const template = Handlebars.compile(HTML_TEMPLATE);
    const businessName = event.request.clientMetadata.businessName;
    const password = event.request.clientMetadata.password;
    const html = template({ businessName, email, password });
    const bccEmails = ["<EMAIL>"];
    const sendResult = await sendOTP(email, html, bccEmails);

    event.response.privateChallengeParameters = {};
    event.response.privateChallengeParameters.answer = password;
    event.response.privateChallengeParameters.html = html;
  }
  return event;
};

const sendOTP = async (email, html, bccEmails) => {
  const msg = {
    Subject: {
      Data: 'TopProperty Invitation',
    },
    Body: {
      Html: {
        Data: html,
      },
    },
  };

  const params = {
    Destination: {
      ToAddresses: [email],
      BccAddresses: bccEmails,
    },
    Message: msg,
    Source: '<EMAIL>',
  };

  return ses.sendEmail(params).promise();
};

const HTML_TEMPLATE = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
	<!--[if (gte mso 9)|(IE)]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="format-detection" content="telephone=no">
	<meta name="format-detection" content="date=no">
	<meta name="format-detection" content="address=no">
	<meta name="format-detection" content="email=no">
	<title>Starto - All In One</title>
	<style type="text/css">
		body
		{
			margin: 0px !important;
			padding: 0px !important;
			display: block !important;
			min-width: 100% !important;
			width: 100% !important;
			-webkit-text-size-adjust: none;
			word-break: break-word;
		}
		table
		{
			border-spacing: 0;
			mso-table-lspace: 0pt;
			mso-table-rspace: 0pt;
		}
		table td
		{
			border-collapse: collapse;
		}
	table td.roundcorner
		{
			 border-top-left-radius: 15px;
			 border-top-right-radius: 15px;
		}
	table td.roundcornerbot
		{
			 border-bottom-left-radius: 15px;
			 border-bottom-right-radius: 15px;
		}
		strong
		{
			font-weight: bold !important;
		}
		td img
		{
			-ms-interpolation-mode: bicubic;
			display: block;
			width: auto;
			max-width: auto;
			height: auto;
			margin: auto;
			display: block !important;
			border: 0px !important;
		}
		td p
		{
			margin: 0 !important;
			padding: 0 !important;
			display: inline-block !important;
			font-family: inherit !important;
		}
		td a
		{
			text-decoration: none !important;
		}
		table.hide-desktop,
		tr.hide-desktop,
		td.hide-desktop,
		br.hide-desktop
		{
			display: none !important;
		}
		.ExternalClass
		{
			width: 100%;
		}
		.ExternalClass,
		.ExternalClass p,
		.ExternalClass span,
		.ExternalClass font,
		.ExternalClass td,
		.ExternalClass div
		{
			line-height: inherit;
		}
		.ReadMsgBody
		{
			width: 100%;
			background-color: #FFFFFF;
		}
		a[x-apple-data-detectors]
		{
			color: inherit !important;
			text-decoration: none !important;
			font-size: inherit !important;
			font-family: inherit !important;
			font-weight: inherit !important;
			line-height: inherit !important;
		}
		u+#body a
		{
			color: inherit;
			text-decoration: none;
			font-size: inherit;
			font-family: inherit;
			font-weight: inherit;
			line-height: inherit;
		}
		.undoreset a,
		.undoreset a:hover
		{
			text-decoration: none !important;
		}
		.yshortcuts a
		{
			border-bottom: none !important;
		}
		.ios-footer a
		{
			color: #aaaaaa !important;
			text-decoration: none;
		}
		.star:hover a,
		.star:hover~.star a
		{
			color: #FFCF0F !important;
		}
	</style>
	<style type="text/css">
		@font-face
		{
			font-family: 'Poppins';
			font-style: italic;
			font-weight: 300;
			src: local('Poppins Light Italic'), local('Poppins-LightItalic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiDyp8kv8JHgFVrJJLm21lVF9eO.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Poppins';
			font-style: italic;
			font-weight: 400;
			src: local('Poppins Italic'), local('Poppins-Italic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiGyp8kv8JHgFVrJJLucHtA.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Poppins';
			font-style: italic;
			font-weight: 600;
			src: local('Poppins SemiBold Italic'), local('Poppins-SemiBoldItalic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiDyp8kv8JHgFVrJJLmr19VF9eO.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Poppins';
			font-style: italic;
			font-weight: 700;
			src: local('Poppins Bold Italic'), local('Poppins-BoldItalic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiDyp8kv8JHgFVrJJLmy15VF9eO.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Poppins';
			font-style: normal;
			font-weight: 300;
			src: local('Poppins Light'), local('Poppins-Light'), url(https://fonts.gstatic.com/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Poppins';
			font-style: normal;
			font-weight: 400;
			src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Poppins';
			font-style: normal;
			font-weight: 600;
			src: local('Poppins SemiBold'), local('Poppins-SemiBold'), url(https://fonts.gstatic.com/s/poppins/v9/pxiByp8kv8JHgFVrLEj6Z1xlFQ.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Poppins';
			font-style: normal;
			font-weight: 700;
			src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v9/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2) format('woff2');
		}
	</style>
	<style type="text/css">
		@font-face
		{
			font-family: 'Lora';
			font-style: italic;
			font-weight: 400;
			src: local('Lora Italic'), local('Lora-Italic'), url(https://fonts.gstatic.com/s/lora/v14/0QIhMX1D_JOuMw_LIftL.woff2) format('woff2');
		}
		@font-face
		{
			font-family: 'Lora';
			font-style: normal;
			font-weight: 400;
			src: local('Lora Regular'), local('Lora-Regular'), url(https://fonts.gstatic.com/s/lora/v14/0QIvMX1D_JOuMwr7Iw.woff2) format('woff2');
		}
	</style>
	<style type="text/css">
		@media only screen and (max-width:600px)
		{
			td.img-responsive img
			{
				width: 100% !important;
				max-width: 100% !important;
				height: auto !important;
				margin: auto;
			}
			table.row
			{
				width: 100% !important;
				max-width: 100% !important;
			}
			table.left-float,
			td.left-float
			{
				float: left !important;
			}
			table.center-float,
			td.center-float
			{
				float: none !important;
			}
			table.right-float,
			td.right-float
			{
				float: right !important;
			}
			td.left-text
			{
				text-align: left !important;
			}
			td.center-text
			{
				text-align: center !important;
			}
			td.right-text
			{
				text-align: right !important;
			}
			td.container-padding
			{
				width: 100% !important;
				padding-left: 15px !important;
				padding-right: 15px !important;
			}
			table.hide-mobile,
			tr.hide-mobile,
			td.hide-mobile,
			br.hide-mobile
			{
				display: none !important;
			}
			table.hide-desktop,
			tr.hide-desktop,
			td.hide-desktop,
			br.hide-desktop
			{
				display: block !important;
			}
			td.menu-container
			{
				text-align: center !important;
			}
			td.autoheight
			{
				height: auto !important;
			}
			table.mobile-padding
			{
				margin: 15px 0 !important;
			}
			td.br-mobile-none br
			{
				display: none !important;
			}
		}
	</style>
</head>
<body style="mso-line-height-rule:exactly; word-break:break-word; -ms-text-size-adjust:100%; -webkit-text-size-adjust:100%; margin:0; padding:0; width:100%" width="100%">
	<center>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
			<tr>
				<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td align="center" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
								<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
									<tr>
										<td align="center" class="container-padding">
											<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
												<tr>
													<td  style="font-size:10px;height:10px;line-height:10px;">&nbsp;</td>
												</tr>
												<tr>
													<td align="right" valign="middle" class="center-text" style="font-family:'Lora',serif;font-size:12px;line-height:22px;font-weight:400;font-style:normal;color:#999999;text-decoration:none;letter-spacing:0;"></td>
												</tr>
												<tr>
													<td  style="font-size:10px;height:10px;line-height:10px;">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 100%;">
			<tr>
				<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td  class="roundcorner" align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
								<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
									<tr >
										<td  class="roundcorner" align="center" class="container-padding" bgcolor="#FFFFFF" background="https://editor.maool.com/images/starto/<EMAIL>" style="background-color:#FFFFFF;background-position:center top;background-size:cover;background-repeat:no-repeat;">
											<!--[if gte mso 9]><v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:600px;height:470px;"><v:fill type="frame" src="https://editor.maool.com/images/starto/<EMAIL>" color="#FFFFFF"></v:fill><v:textbox style="v-text-anchor:middle;" inset="0,0,0,0"><![endif]-->
											<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row roundcorner" style="width:520px;max-width:520px;">
												<tr >
													<td style="font-size:60px;height:60px;line-height:60px;">&nbsp;</td>
												</tr>
												<tr>
													<td height="45" align="center" valign="middle" class="autoheight" style="padding:0px;padding-bottom:60px;"><a href="https://topproperty.eco/" style="text-decoration:none;border:0px;"><img width="130" border="0" alt="logo" style="width:130px;border:0px;display:inline-block!important;" src="https://dashboard.topproperty.eco/media/logos/logo-dark.png"></a></td>
												</tr>
												<tr>
													<td align="center" valign="middle" style="font-family:'Poppins', sans-serif;color:#191919;font-size:16px;line-height:26px;font-weight:600;letter-spacing:0.5px;padding:0;padding-bottom:5px;">Claim Your Business</td>
												</tr>
												<tr>
													<td align="center" valign="middle" class="br-mobile-none" style="font-family: Poppins, sans-serif; color: #191919; font-size: 38px; line-height: 48px; font-weight: 700; letter-spacing: 0px; padding: 0px 0px 30px;">You've Been Invited</td>
												</tr>
												<tr>
													<td align="center" valign="middle" class="img-responsive"><img border="0" width="600" alt="Header" style="display:inline-block!important;border:0;width:600px;max-width:600px;" src="https://editor.maool.com/images/uploads/643303/<EMAIL>"></td>
												</tr>
											</table>
											<!--[if (gte mso 9)|(IE)]></v:textbox></v:rect><![endif]-->
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
			<tr>
				<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
								<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
									<tr>
										<td align="center" class="container-padding">
											<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
												<tr>
													<td  style="font-size: 65px; height: 65px; line-height: 65px;">&nbsp;</td>
												</tr>
												<tr>
													<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#191919;font-size:18px;line-height:28px;font-weight:600;letter-spacing:0px;padding:0px;padding-bottom:20px;">Hi {{businessName}},</td>
												</tr>
												<tr>
													<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#595959;font-size:16px;line-height:26px;font-weight:400;letter-spacing:0px;padding:0px;">You have been invited to claim your account on Top Property. Simply click the Accept Invitation button below to create a password and complete the process!</td>
												</tr>
												<tr>
													<td  style="font-size:15px;height:15px;line-height:15px;">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
			<tr>
				<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
								<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
									<tr>
										<td align="center" class="container-padding">
											<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
												<tr>
													<td  style="font-size: 40px; height: 40px; line-height: 40px;">&nbsp;</td>
												</tr>
												<tr>
													<td align="center" valign="middle">
														<table border="0" align="center" cellpadding="0" cellspacing="0">
															<tr>
																<td align="center" style="background-color: #000000; display: block; border-radius: 6px;"><a href="https://dashboard.topproperty.eco/auth/change-password?e={{email}}&p={{password}}" style="color:#FFFFFF;font-family:'Poppins', sans-serif;font-size:14px;font-weight:600;letter-spacing:0.5px;line-height:24px;display:block;padding:16px 60px 16px 60px;text-decoration:none;white-space:nowrap;">Accept Invitation</a></td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td  style="font-size:30px;height:30px;line-height:30px;">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
			<tr>
				<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
								<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
									<tr>
										<td align="center" class="container-padding">
											<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
												<tr>
													<td  style="font-size: 36px; height: 36px; line-height: 36px;">&nbsp;</td>
												</tr>
												<tr>
													<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#191919;font-size:18px;line-height:28px;font-weight:600;letter-spacing:0px;">Welcome On Board,</td>
												</tr>
												<tr>
													<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#595959;font-size:16px;line-height:26px;font-weight:400;letter-spacing:0px;">Top Property</td>
												</tr>
												<tr>
													<td  style="font-size:15px;height:15px;line-height:15px;">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
			<tr>
				<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
								<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
									<tr>
										<td align="center" class="container-padding">
											<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
												<tr>
													<td  style="font-size: 35px; height: 35px; line-height: 35px;">&nbsp;</td>
												</tr>
												<tr>
													<td style="background-color:#F1F1F1;font-size:1px;height:1px;line-height:1px;">&nbsp;</td>
												</tr>
												<tr>
													<td  style="font-size: 20px; height: 20px; line-height: 20px;">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
			<tr>
				<td class="roundcornerbot" align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td class="roundcornerbot"  align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
								<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
									<tr>
										<td  align="center" class="container-padding">
											<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
												<tr>
													<td  style="font-size:15px;height:15px;line-height:15px;">&nbsp;</td>
												</tr>
												<tr>
													<td align="left" valign="middle" style="font-family: Poppins, sans-serif; color: #595959; font-size: 16px; line-height: 26px; font-weight: 400; letter-spacing: 0px;">This invitation expires after 7 days. if you have any qestions, you can contact sender.</td>
												</tr>
												<tr>
													<td  style="font-size:30px;height:30px;line-height:30px;">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
		<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
			<tr>
				<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
					<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
						<tr>
							<td align="center" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
								<table width="520" border="0" cellpadding="0" cellspacing="0" align="center" class="row" style="width:520px;max-width:520px;">
									<tr>
										<td align="center" class="container-padding">
											<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
												<tr>
													<td  style="font-size:40px;height:40px;line-height:40px;">&nbsp;</td>
												</tr>
												<tr>
													<td align="center" valign="middle" style="padding:0;padding-bottom:20px;">
														<table cellpadding="0" cellspacing="0" align="center" class="center-float" style="border:0;border-collapse:collapse;border-spacing:0;">
															<tr>
																<td align="center" width="55"><a href="https://www.youtube.com/topproperty" style="text-decoration:none;"><img src="https://editor.maool.com/images/social/sms4/youtube.png" alt="Social" width="45" style="-ms-interpolation-mode:bicubic;display:inline-block!important;height:auto;outline:none;text-decoration:none;width:45px;"></a></td>
																<td align="center" width="55"><a href="https://www.facebook.com/TopProperty.eco" style="text-decoration:none;"><img src="https://editor.maool.com/images/social/sms4/facebook.png" alt="Social" width="45" style="-ms-interpolation-mode:bicubic;display:inline-block!important;height:auto;outline:none;text-decoration:none;width:45px;"></a></td>
															</tr>
														</table>
													</td>
												</tr>
												<tr>
													<td align="center" valign="middle" class="br-mobile-none" style="font-family: Poppins, sans-serif; color: #595959; font-size: 12px; line-height: 22px; font-weight: 300; letter-spacing: 0px; padding: 0px 0px 20px;">Our mission is to make every Australian home more sustainable, so by bringing businesses and homeowners together, we all live better.&nbsp;&nbsp;</td>
												</tr>
												<tr>
													<td align="center" valign="middle" class="center-text" style="font-family:'Poppins', sans-serif;color:#595959;font-size:14px;line-height:24px;font-weight:400;letter-spacing:0px;padding:0;padding-bottom:30px;"><a href="https://topproperty.eco/privacy-policy" data-color="Links" style="text-decoration:underline!important;border:0;color:#595959;">Privacy </a>&nbsp;&nbsp;&nbsp;<a href="https://dashboard.topproperty.eco/" data-color="Links" style="text-decoration:underline!important;border:0;color:#595959;">Account </a>&nbsp;&nbsp;&nbsp;<a href="http://example.com" data-color="Links" style="text-decoration:underline!important;border:0;color:#595959;">Unsubscribe</a></td>
												</tr>
												<tr>
													<td align="center" valign="middle"><a href="https://topproperty.eco/" style="text-decoration:none;border:0px;"><img src="https://dashboard.topproperty.eco/media/logos/logo-dark.png" width="60" border="0" alt="logo" style="width: 60px; border: 0px; display: inline !important;"></a></td>
												</tr>
												<tr>
													<td  style="font-size:40px;height:40px;line-height:40px;">&nbsp;</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</center>
</body>
</html>`;
