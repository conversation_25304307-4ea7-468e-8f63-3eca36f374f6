{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"11.0.3\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"REDIRECTURL": {"Type": "String", "Default": ""}, "EMAILMESSAGE": {"Type": "String", "Default": ""}, "EMAILSUBJECT": {"Type": "String", "Default": ""}, "modules": {"Type": "String", "Default": "", "Description": "Comma-delimited list of modules to be executed by a lambda trigger. Sent to resource as an env variable."}, "resourceName": {"Type": "String", "Default": ""}, "trigger": {"Type": "String", "Default": "true"}, "functionName": {"Type": "String", "Default": ""}, "roleName": {"Type": "String", "Default": ""}, "parentResource": {"Type": "String", "Default": ""}, "parentStack": {"Type": "String", "Default": ""}, "env": {"Type": "String"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "tpdashboardf54f2862f54f2862CustomMessage", {"Fn::Join": ["", ["tpdashboardf54f2862f54f2862CustomMessage", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "MODULES": {"Ref": "modules"}, "REGION": {"Ref": "AWS::Region"}, "REDIRECTURL": {"Ref": "REDIRECTURL"}, "EMAILSUBJECT": {"Ref": "EMAILSUBJECT"}, "EMAILMESSAGE": {"Ref": "EMAILMESSAGE"}, "RESOURCENAME": {"Ref": "resourceName"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs16.x", "Timeout": 25, "Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "tpdashboardf54f2862f54f2862CustomMessage", {"Fn::Join": ["", ["tpdashboardf54f2862f54f2862CustomMessage", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "CustomLambdaExecutionPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "custom-lambda-execution-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": ["ses:SendEmail"], "Resource": ["*"], "Effect": "Allow"}]}, "Roles": [{"Ref": "LambdaExecutionRole"}]}, "DependsOn": "LambdaExecutionRole"}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}}}