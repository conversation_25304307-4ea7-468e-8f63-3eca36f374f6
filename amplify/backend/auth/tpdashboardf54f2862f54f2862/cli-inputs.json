{"version": "1", "cognitoConfig": {"identityPoolName": "tpdashboardf54f2862_identitypool_f54f2862", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "tpdashf54f2862", "userPoolName": "tpdashboardf54f2862_userpool_f54f2862", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "tpdashf54f2862_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "f54f2862", "resourceName": "tpdashboardf54f2862f54f2862", "authSelections": "identityPoolAndUserPool", "useDefault": "manual", "thirdPartyAuth": true, "authProviders": ["accounts.google.com"], "usernameAttributes": ["email"], "userPoolGroups": false, "adminQueries": false, "triggers": {"CreateAuthChallenge": ["boilerplate-create-challenge"], "CustomMessage": ["custom"], "DefineAuthChallenge": ["boilerplate-define-challenge"], "PreSignup": ["custom"], "VerifyAuthChallengeResponse": ["boilerplate-verify"]}, "hostedUI": true, "hostedUIDomainName": "tpdashboardf54f2862-f54f2862", "authProvidersUserPool": ["Google"], "hostedUIProviderMeta": "[{\"ProviderName\":\"Google\",\"authorize_scopes\":\"openid email profile\",\"AttributeMapping\":{\"email\":\"email\",\"username\":\"sub\"}}]", "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "dependsOn": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862CreateAuthChallenge", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "tpdashboardf54f2862f54f2862DefineAuthChallenge", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "tpdashboardf54f2862f54f2862PreSignup", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "tpdashboardf54f2862f54f2862CustomMessage", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}], "permissions": [], "authTriggerConnections": "[\n  {\n    \"triggerType\": \"CreateAuth<PERSON>hallenge\",\n    \"lambdaFunctionName\": \"tpdashboardf54f2862f54f2862CreateAuthChallenge\"\n  },\n  {\n    \"triggerType\": \"CustomMessage\",\n    \"lambdaFunctionName\": \"tpdashboardf54f2862f54f2862CustomMessage\"\n  },\n  {\n    \"triggerType\": \"DefineAuthChallenge\",\n    \"lambdaFunctionName\": \"tpdashboardf54f2862f54f2862DefineAuthChallenge\"\n  },\n  {\n    \"triggerType\": \"PreSignUp\",\n    \"lambdaFunctionName\": \"tpdashboardf54f2862f54f2862PreSignup\"\n  },\n  {\n    \"triggerType\": \"VerifyAuthChallengeResponse\",\n    \"lambdaFunctionName\": \"tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse\"\n  }\n]", "parentStack": {"Ref": "AWS::StackId"}, "oAuthMetadata": "{\"AllowedOAuthFlows\":[\"code\"],\"AllowedOAuthScopes\":[\"phone\",\"email\",\"openid\",\"profile\",\"aws.cognito.signin.user.admin\"],\"CallbackURLs\":[\"https://dashboard.topproperty.eco/\"],\"LogoutURLs\":[\"https://dashboard.topproperty.eco/\"]}"}}