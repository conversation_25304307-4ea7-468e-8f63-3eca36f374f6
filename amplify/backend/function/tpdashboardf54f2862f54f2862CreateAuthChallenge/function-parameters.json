{"trigger": true, "modules": ["boilerplate-create-challenge"], "parentResource": "tpdashboardf54f2862f54f2862", "functionName": "tpdashboardf54f2862f54f2862CreateAuthChallenge", "resourceName": "tpdashboardf54f2862f54f2862CreateAuthChallenge", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/repo/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/CreateAuthChallenge", "triggerTemplate": "CreateAuthChallenge.json.ejs", "triggerEventPath": "CreateAuthChallenge.event.json", "roleName": "tpdashboardf54f2862f54f2862CreateAuthChallenge", "skipEdit": true}