{"auth": {"tpdashboardf54f2862f54f2862": {"customAuth": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "tpdashboardf54f2862f54f2862CreateAuthChallenge", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "tpdashboardf54f2862f54f2862DefineAuthChallenge", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "tpdashboardf54f2862f54f2862PreSignup", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "tpdashboardf54f2862f54f2862CustomMessage", "triggerProvider": "Cognito"}], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": ["GOOGLE"], "usernameAttributes": ["EMAIL"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}, "function": {"tpdashboardf54f2862f54f2862CreateAuthChallenge": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "tpdashboardf54f2862f54f2862CustomMessage": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "tpdashboardf54f2862f54f2862DefineAuthChallenge": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "tpdashboardf54f2862f54f2862PreSignup": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}}, "parameters": {"AMPLIFY_auth_tpdashboardf54f2862f54f2862_googleClientId": {"usedBy": [{"category": "auth", "resourceName": "tpdashboardf54f2862f54f2862"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862CreateAuthChallenge_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862CreateAuthChallenge"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862CreateAuthChallenge_s3Key": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862CreateAuthChallenge"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862CustomMessage_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862CustomMessage"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862CustomMessage_s3Key": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862CustomMessage"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862DefineAuthChallenge_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862DefineAuthChallenge"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862DefineAuthChallenge_s3Key": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862DefineAuthChallenge"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862PreSignup_DOMAINALLOWLIST": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862PreSignup"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862PreSignup_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862PreSignup"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862PreSignup_s3Key": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862PreSignup"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse"}]}, "AMPLIFY_function_tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse_s3Key": {"usedBy": [{"category": "function", "resourceName": "tpdashboardf54f2862f54f2862VerifyAuthChallengeResponse"}]}, "AMPLIFY_storage_tpdashboard2c0332af_bucketName": {"usedBy": [{"category": "storage", "resourceName": "tpdashboard2c0332af"}]}, "AMPLIFY_storage_tpdashboard2c0332af_region": {"usedBy": [{"category": "storage", "resourceName": "tpdashboard2c0332af"}]}}, "storage": {"tpdashboard2c0332af": {"dependsOn": [], "providerPlugin": "awscloudformation", "service": "S3", "serviceType": "imported"}}}