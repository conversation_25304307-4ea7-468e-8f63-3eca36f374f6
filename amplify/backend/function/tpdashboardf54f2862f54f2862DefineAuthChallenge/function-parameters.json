{"trigger": true, "modules": ["boilerplate-define-challenge"], "parentResource": "tpdashboardf54f2862f54f2862", "functionName": "tpdashboardf54f2862f54f2862DefineAuthChallenge", "resourceName": "tpdashboardf54f2862f54f2862DefineAuthChallenge", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/repo/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/DefineAuthChallenge", "triggerTemplate": "DefineAuthChallenge.json.ejs", "triggerEventPath": "DefineAuthChallenge.event.json", "roleName": "tpdashboardf54f2862f54f2862DefineAuthChallenge", "skipEdit": true}