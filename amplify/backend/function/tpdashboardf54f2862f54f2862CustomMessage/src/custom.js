/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
// paste your forgot password template here dont use handle bars
// email sample
const generate_email_body = (confirmCode, email) => `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
<!--[if (gte mso 9)|(IE)]><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml><![endif]-->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="format-detection" content="telephone=no">
<meta name="format-detection" content="date=no">
<meta name="format-detection" content="address=no">
<meta name="format-detection" content="email=no">
<title>Starto - All In One</title>
<style type="text/css">
body
{margin: 0px !important;padding: 0px !important;display: block !important;min-width: 100% !important;width: 100% !important;-webkit-text-size-adjust: none;word-break: break-word;}
table{border-spacing: 0;mso-table-lspace: 0pt;mso-table-rspace: 0pt;}
table td{border-collapse: collapse;}table td.roundcorner{border-top-left-radius: 15px;border-top-right-radius: 15px;}table td.roundcornerbot{border-bottom-left-radius: 15px;border-bottom-right-radius: 15px;}strong{font-weight: bold !important;}td img{-ms-interpolation-mode: bicubic;display: block;width: auto;max-width: auto;height: auto;margin: auto;display: block !important;border: 0px !important;}td p{margin: 0 !important;padding: 0 !important;display: inline-block !important;font-family: inherit !important;}td a{text-decoration: none !important;}
table.hide-desktop,
tr.hide-desktop,
td.hide-desktop,
br.hide-desktop
{
display: none !important;
}
.ExternalClass
{
width: 100%;
}
.ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div
{
line-height: inherit;
}
.ReadMsgBody
{
width: 100%;
background-color: #FFFFFF;
}
a[x-apple-data-detectors]
{
color: inherit !important;
text-decoration: none !important;
font-size: inherit !important;
font-family: inherit !important;
font-weight: inherit !important;
line-height: inherit !important;
}
u+#body a
{
color: inherit;
text-decoration: none;
font-size: inherit;
font-family: inherit;
font-weight: inherit;
line-height: inherit;
}
.undoreset a,
.undoreset a:hover
{
text-decoration: none !important;
}
.yshortcuts a
{
border-bottom: none !important;
}
.ios-footer a
{
color: #aaaaaa !important;
text-decoration: none;
}
.star:hover a,
.star:hover~.star a
{
color: #FFCF0F !important;
}
</style>
<style type="text/css">
@font-face
{
font-family: 'Poppins';
font-style: italic;
font-weight: 400;
src: local('Poppins Italic'), local('Poppins-Italic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiGyp8kv8JHgFVrJJLucHtA.woff2) format('woff2');
}
@font-face
{
font-family: 'Poppins';
font-style: italic;
font-weight: 500;
src: local('Poppins Medium Italic'), local('Poppins-MediumItalic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiDyp8kv8JHgFVrJJLmg1hVF9eO.woff2) format('woff2');
}
@font-face
{
font-family: 'Poppins';
font-style: italic;
font-weight: 600;
src: local('Poppins SemiBold Italic'), local('Poppins-SemiBoldItalic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiDyp8kv8JHgFVrJJLmr19VF9eO.woff2) format('woff2');
}
@font-face
{
font-family: 'Poppins';
font-style: italic;
font-weight: 700;
src: local('Poppins Bold Italic'), local('Poppins-BoldItalic'), url(https://fonts.gstatic.com/s/poppins/v9/pxiDyp8kv8JHgFVrJJLmy15VF9eO.woff2) format('woff2');
}
@font-face
{
font-family: 'Poppins';
font-style: normal;
font-weight: 400;
src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
}
@font-face
{
font-family: 'Poppins';
font-style: normal;
font-weight: 500;
src: local('Poppins Medium'), local('Poppins-Medium'), url(https://fonts.gstatic.com/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2) format('woff2');
}
@font-face
{
font-family: 'Poppins';
font-style: normal;
font-weight: 600;
src: local('Poppins SemiBold'), local('Poppins-SemiBold'), url(https://fonts.gstatic.com/s/poppins/v9/pxiByp8kv8JHgFVrLEj6Z1xlFQ.woff2) format('woff2');
}
@font-face
{
font-family: 'Poppins';
font-style: normal;
font-weight: 700;
src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v9/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2) format('woff2');
}
</style>
<style type="text/css">
@font-face
{
font-family: 'Lora';
font-style: italic;
font-weight: 400;
src: local('Lora Italic'), local('Lora-Italic'), url(https://fonts.gstatic.com/s/lora/v14/0QIhMX1D_JOuMw_LIftL.woff2) format('woff2');
}
@font-face
{
font-family: 'Lora';
font-style: normal;
font-weight: 400;
src: local('Lora Regular'), local('Lora-Regular'), url(https://fonts.gstatic.com/s/lora/v14/0QIvMX1D_JOuMwr7Iw.woff2) format('woff2');
}
</style>
<style type="text/css">
@media only screen and (max-width:600px)
{
td.img-responsive img
{
width: 100% !important;
max-width: 100% !important;
height: auto !important;
margin: auto;
}
table.row
{
width: 100% !important;
max-width: 100% !important;
}
table.left-float,
td.left-float
{
float: left !important;
}
table.center-float,
td.center-float
{
float: none !important;
}
table.right-float,
td.right-float
{
float: right !important;
}
td.left-text
{
text-align: left !important;
}
td.center-text
{
text-align: center !important;
}
td.right-text
{
text-align: right !important;
}
td.container-padding
{
width: 100% !important;
padding-left: 15px !important;
padding-right: 15px !important;
}
table.hide-mobile,
tr.hide-mobile,
td.hide-mobile,
br.hide-mobile
{
display: none !important;
}
table.hide-desktop,
tr.hide-desktop,
td.hide-desktop,
br.hide-desktop
{
display: block !important;
}
td.menu-container
{
text-align: center !important;
}
td.autoheight
{
height: auto !important;
}
table.mobile-padding
{
margin: 15px 0 !important;
}
td.br-mobile-none br
{
display: none !important;
}
}
</style>
</head>
<body style="mso-line-height-rule:exactly; word-break:break-word; -ms-text-size-adjust:100%; -webkit-text-size-adjust:100%; margin:0; padding:0; width:100%" width="100%">
<center>
<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
<tr>
<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td align="center" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
<tr>
<td align="center" class="container-padding">
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td  style="font-size:10px;height:10px;line-height:10px;">&nbsp;</td>
</tr>
<tr>
<td align="right" valign="middle" class="center-text" style="font-family:'Lora',serif;font-size:12px;line-height:22px;font-weight:400;font-style:normal;color:#999999;text-decoration:none;letter-spacing:0;"></td>
</tr>
<tr>
<td  style="font-size:10px;height:10px;line-height:10px;">&nbsp;</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table   class="roundcorner" border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 100%;">
<tr>
<td   class="roundcorner" align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table   class="roundcorner" border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td   class="roundcorner" align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
<table   class="roundcorner"   class="roundcorner" border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td   class="roundcorner" align="center" class="container-padding" bgcolor="#FFFFFF" background="https://editor.maool.com/images/uploads/643303/1688340848-Screenshot_2023-07-01_at_2.57.01_pm.png" style="background-color:#FFFFFF;background-position:center top;background-size:cover;background-repeat:no-repeat;">
<!--[if gte mso 9]><v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:600px;height:470px;"><v:fill type="frame" src="https://editor.maool.com/images/uploads/643303/1688340848-Screenshot_2023-07-01_at_2.57.01_pm.png" color="#FFFFFF"></v:fill><v:textbox style="v-text-anchor:middle;" inset="0,0,0,0"><![endif]-->
<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
<tr>
<td  style="font-size:60px;height:60px;line-height:60px;">&nbsp;</td>
</tr>
<tr>
<td height="45" align="center" valign="middle" class="autoheight" style="padding:0px;padding-bottom:60px;"><a href="https://topproperty.eco/" style="text-decoration:none;border:0px;"><img width="130" border="0" alt="logo" style="width:130px;border:0px;display:inline-block!important;" src="https://editor.maool.com/images/uploads/643303/**********-logo_full_black.png"></a></td>
</tr>
<tr>
<td align="center" valign="middle" style="font-family:'Poppins', sans-serif;color:#191919;font-size:16px;line-height:26px;font-weight:600;letter-spacing:0.5px;padding:0;padding-bottom:5px;">Verify Your Email</td>
</tr>
<tr>
<td align="center" valign="middle" class="br-mobile-none" style="font-family: Poppins, sans-serif; color: #191919; font-size: 38px; line-height: 48px; font-weight: 700; letter-spacing: 0px; padding: 0px 0px 30px;">Reset Password</td>
</tr>
<tr>
<td align="center" valign="middle" class="img-responsive"><img border="0" width="6000" alt="Header" style="border: 0px; width: 6000px; max-width: 600px; display: inline-block !important;" src="https://editor.maool.com/images/uploads/643303/1688187357-reset-password-verification.png"></td>
</tr>
</table>
<!--[if (gte mso 9)|(IE)]></v:textbox></v:rect><![endif]-->
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
<tr>
<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
<tr>
<td align="center" class="container-padding">
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td  style="font-size:30px;height:30px;line-height:30px;">&nbsp;</td>
</tr>
<tr>
<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#191919;font-size:18px;line-height:28px;font-weight:600;letter-spacing:0px;padding:0px;padding-bottom:20px;">Hi ${email},&nbsp;</td>
</tr>
<tr>
<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#595959;font-size:16px;line-height:26px;font-weight:400;letter-spacing:0px;padding:0px;padding-bottom:40px;">We've received a request to reset your Top Property account password. Simply enter the verification code below and create a new password to access your account.</td>
</tr>
<tr>
<td  style="font-size: 3px; height: 3px; line-height: 3px;">&nbsp;</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 100%; z-index: 1;">
<tr>
<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
<tr>
<td align="center" class="container-padding">
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td  style="font-size: 0px; height: 0px; line-height: 0px;">&nbsp;</td>
</tr>
<tr>
<td align="center" valign="middle" bgcolor="#F5F5F5" class="container-padding" style="background-color: #f5f5f6; border-radius: 8px;">
<table border="0" width="460" cellpadding="0" cellspacing="0" align="center" class="row" style="width:460px;max-width:460px;">
<tr>
    <td style="font-size:30px;height:30px;line-height:30px;">&nbsp;</td>
</tr>
<tr>
    <td align="left" valign="middle" style="font-family: Poppins, sans-serif; color: #040404; font-size: 18px; line-height: 28px; font-weight: 500; letter-spacing: 0px;">Verification Code:&nbsp; &nbsp;${confirmCode}<br></td>
</tr>
<tr>
    <td style="font-size:30px;height:30px;line-height:30px;">&nbsp;</td>
</tr>
</table>
</td>
</tr>
<tr>
<td  style="font-size: 38px; height: 38px; line-height: 38px;">&nbsp;</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
<tr>
<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
<tr>
<td align="center" class="container-padding">
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td  style="font-size: 10px; height: 10px; line-height: 10px;">&nbsp;</td>
</tr>
<tr>
<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#191919;font-size:18px;line-height:28px;font-weight:600;letter-spacing:0px;">Kind Regards,</td>
</tr>
<tr>
<td align="left" valign="middle" style="font-family:'Poppins', sans-serif;color:#595959;font-size:16px;line-height:26px;font-weight:400;letter-spacing:0px;">Top Property</td>
</tr>
<tr>
<td  style="font-size:15px;height:15px;line-height:15px;">&nbsp;</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width:100%;max-width:100%;">
<tr>
<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
<tr>
<td align="center" class="container-padding">
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td  style="font-size: 32px; height: 32px; line-height: 32px;">&nbsp;</td>
</tr>
<tr>
<td style="background-color:#F1F1F1;font-size:1px;height:1px;line-height:1px;">&nbsp;</td>
</tr>
<tr>
<td  style="font-size:15px;height:15px;line-height:15px;">&nbsp;</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table  class="roundcornerbot"  class="roundcornerbot"  class="roundcornerbot"  class="roundcornerbot" border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 100%; z-index: 1;">
<tr>
<td  class="roundcornerbot"  class="roundcornerbot"  class="roundcornerbot" align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table  class="roundcornerbot"  class="roundcornerbot" border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td  class="roundcornerbot" align="center" bgcolor="#FFFFFF" style="background-color:#FFFFFF;">
<table border="0" width="520" align="center" cellpadding="0" cellspacing="0" class="row" style="width:520px;max-width:520px;">
<tr>
<td align="center" class="container-padding">
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td  style="font-size:15px;height:15px;line-height:15px;">&nbsp;</td>
</tr>
<tr>
<td align="left" valign="middle" style="font-family: Poppins, sans-serif; color: #595959; font-size: 16px; line-height: 26px; font-weight: 400; letter-spacing: 0px;">You don't have to do anything if you didn't request to change your Top Property password. Just log in with your existing password and disregard this email. </td>
</tr>
<tr>
<td  style="font-size:30px;height:30px;line-height:30px;">&nbsp;</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table data-premium-module="" border="0" width="100%" align="center" cellpadding="0" cellspacing="0" style="width: 100%; max-width: 100%;">
<tr>
<td align="center" valign="middle" bgcolor="#F1F1F1" style="background-color: #f5f5f6;">
<table border="0" width="600" align="center" cellpadding="0" cellspacing="0" class="row" style="width:600px;max-width:600px;">
<tr>
<td align="center" bgcolor="#FFFFFF" style="background-color: #f5f5f6;">
<table width="520" border="0" cellpadding="0" cellspacing="0" align="center" class="row" style="width:520px;max-width:520px;">
<tr>
<td align="center" class="container-padding">
<table border="0" width="100%" cellpadding="0" cellspacing="0" align="center" style="width:100%; max-width:100%;">
<tr>
<td  style="font-size:40px;height:40px;line-height:40px;">&nbsp;</td>
</tr>
<tr>
<td align="center" valign="middle" style="padding:0;padding-bottom:20px;">

</td>
</tr>
<tr>
<td align="center" valign="middle" class="br-mobile-none" style="font-family: Poppins, DejaVu Sans, Verdana, sans-serif; color: #595959; font-size: 12px; line-height: 22px; font-weight: 400; letter-spacing: 0px; padding: 0px 0px 20px;">Our mission is to make every Australian home more sustainable, so by bringing businesses and homeowners together, we all live better.&nbsp;&nbsp;</td>
</tr>
<tr>
<td align="center" valign="middle" class="center-text" style="font-family: Poppins, DejaVu Sans, Verdana, sans-serif; color: #595959; font-size: 14px; line-height: 24px; font-weight: 400; letter-spacing: 0px; padding: 0px 0px 30px;"><a href="https://topproperty.eco/privacy-policy" data-color="Links" style="text-decoration:underline!important;border:0;color:#595959;">Privacy Policy&nbsp;</a>&nbsp;&nbsp;&nbsp;<a href="https://dashboard.topproperty.eco/" data-color="Links" style="text-decoration:underline!important;border:0;color:#595959;">Account </a>&nbsp;&nbsp;&nbsp;<a href="https://topproperty.eco/contact-us" data-color="Links" style="text-decoration:underline!important;border:0;color:#595959;">Contact Us</a></td>
</tr>
<tr>
<td align="center" valign="middle"><a href="https://topproperty.eco/" style="text-decoration:none;border:0px;"><img width="80" border="0" alt="logo" style="width: 80px; border: 0px; display: inline !important;" src="https://editor.maool.com/images/uploads/643303/**********-logo_full_black.png"></a></td>
</tr>
<tr>
<td  style="font-size:40px;height:40px;line-height:40px;">&nbsp;</td></tr></table></td></tr></table></td></tr></table></td></tr></table></center></body></html>
`;
exports.handler = async (event) => {
  const email = event.request.userAttributes.email;
  console.log(email, event.request);
  if (event.triggerSource === 'CustomMessage_ForgotPassword') {
    const confirmCode = event.request.codeParameter;
    const message = `Your confirmation code is ${event.request.codeParameter}.`;

    event.response.emailMessage = generate_email_body(confirmCode, email);
    event.response.emailSubject = 'TopProperty Forgot Password Confirmation Code.';
  }
  return event;
};
